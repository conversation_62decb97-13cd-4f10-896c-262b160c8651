{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 23505, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 23505, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 23505, "tid": 243, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 23505, "tid": 243, "ts": 1748962271654088, "dur": 435, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 23505, "tid": 243, "ts": 1748962271659648, "dur": 525, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 23505, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 23505, "tid": 1, "ts": 1748962270773977, "dur": 7267, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23505, "tid": 1, "ts": 1748962270781275, "dur": 54153, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 23505, "tid": 1, "ts": 1748962270835437, "dur": 41343, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 23505, "tid": 243, "ts": 1748962271660176, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 23505, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270772380, "dur": 4825, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270777208, "dur": 869441, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270777936, "dur": 2320, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270780259, "dur": 1027, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270781288, "dur": 14351, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270795648, "dur": 288, "ph": "X", "name": "ProcessMessages 1929", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270795938, "dur": 40, "ph": "X", "name": "ReadAsync 1929", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270795983, "dur": 3, "ph": "X", "name": "ProcessMessages 8177", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270795988, "dur": 31, "ph": "X", "name": "ReadAsync 8177", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796024, "dur": 1, "ph": "X", "name": "ProcessMessages 1086", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796026, "dur": 40, "ph": "X", "name": "ReadAsync 1086", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796068, "dur": 1, "ph": "X", "name": "ProcessMessages 1388", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796070, "dur": 32, "ph": "X", "name": "ReadAsync 1388", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796106, "dur": 25, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796156, "dur": 23, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796195, "dur": 27, "ph": "X", "name": "ReadAsync 1125", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796225, "dur": 23, "ph": "X", "name": "ReadAsync 1207", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796250, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270796252, "dur": 1239, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797492, "dur": 3, "ph": "X", "name": "ProcessMessages 8089", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797497, "dur": 27, "ph": "X", "name": "ReadAsync 8089", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797537, "dur": 30, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797572, "dur": 38, "ph": "X", "name": "ReadAsync 1284", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797612, "dur": 1, "ph": "X", "name": "ProcessMessages 1240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797615, "dur": 24, "ph": "X", "name": "ReadAsync 1240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797644, "dur": 37, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797684, "dur": 37, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797723, "dur": 23, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797749, "dur": 38, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797790, "dur": 1, "ph": "X", "name": "ProcessMessages 1362", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797791, "dur": 26, "ph": "X", "name": "ReadAsync 1362", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797823, "dur": 45, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797871, "dur": 30, "ph": "X", "name": "ReadAsync 865", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797903, "dur": 42, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797947, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797954, "dur": 34, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797998, "dur": 1, "ph": "X", "name": "ProcessMessages 1654", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270797999, "dur": 29, "ph": "X", "name": "ReadAsync 1654", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798034, "dur": 19, "ph": "X", "name": "ReadAsync 945", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798064, "dur": 34, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798099, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798108, "dur": 129, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798257, "dur": 8, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798272, "dur": 512, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798802, "dur": 1, "ph": "X", "name": "ProcessMessages 1197", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798804, "dur": 22, "ph": "X", "name": "ReadAsync 1197", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798828, "dur": 54, "ph": "X", "name": "ReadAsync 1048", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798913, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798937, "dur": 24, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270798963, "dur": 35, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799004, "dur": 20, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799032, "dur": 31, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799065, "dur": 35, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799112, "dur": 70, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799183, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799205, "dur": 106, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799313, "dur": 20, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799335, "dur": 34, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799376, "dur": 95, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799472, "dur": 1, "ph": "X", "name": "ProcessMessages 1443", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799473, "dur": 33, "ph": "X", "name": "ReadAsync 1443", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799508, "dur": 45, "ph": "X", "name": "ReadAsync 783", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799556, "dur": 24, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799581, "dur": 1, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799582, "dur": 353, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799938, "dur": 3, "ph": "X", "name": "ProcessMessages 6579", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799942, "dur": 17, "ph": "X", "name": "ReadAsync 6579", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270799961, "dur": 69, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800037, "dur": 26, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800065, "dur": 1, "ph": "X", "name": "ProcessMessages 1881", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800067, "dur": 21, "ph": "X", "name": "ReadAsync 1881", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800093, "dur": 68, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800162, "dur": 1, "ph": "X", "name": "ProcessMessages 1474", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800163, "dur": 36, "ph": "X", "name": "ReadAsync 1474", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800223, "dur": 131, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800356, "dur": 20, "ph": "X", "name": "ReadAsync 963", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800383, "dur": 30, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800415, "dur": 47, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800467, "dur": 1, "ph": "X", "name": "ProcessMessages 1214", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800469, "dur": 18, "ph": "X", "name": "ReadAsync 1214", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800491, "dur": 70, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800562, "dur": 1, "ph": "X", "name": "ProcessMessages 1127", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800563, "dur": 49, "ph": "X", "name": "ReadAsync 1127", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800620, "dur": 24, "ph": "X", "name": "ReadAsync 817", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800647, "dur": 67, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800715, "dur": 1, "ph": "X", "name": "ProcessMessages 1111", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800716, "dur": 28, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800757, "dur": 21, "ph": "X", "name": "ReadAsync 939", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800780, "dur": 26, "ph": "X", "name": "ReadAsync 922", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800813, "dur": 166, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800980, "dur": 1, "ph": "X", "name": "ProcessMessages 1348", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270800982, "dur": 48, "ph": "X", "name": "ReadAsync 1348", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801033, "dur": 87, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801160, "dur": 1, "ph": "X", "name": "ProcessMessages 1649", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801187, "dur": 69, "ph": "X", "name": "ReadAsync 1649", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801280, "dur": 31, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801312, "dur": 1, "ph": "X", "name": "ProcessMessages 1811", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801314, "dur": 76, "ph": "X", "name": "ReadAsync 1811", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801392, "dur": 1, "ph": "X", "name": "ProcessMessages 1849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801394, "dur": 116, "ph": "X", "name": "ReadAsync 1849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801511, "dur": 1, "ph": "X", "name": "ProcessMessages 2995", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801542, "dur": 119, "ph": "X", "name": "ReadAsync 2995", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801662, "dur": 1, "ph": "X", "name": "ProcessMessages 2841", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801687, "dur": 25, "ph": "X", "name": "ReadAsync 2841", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801713, "dur": 1, "ph": "X", "name": "ProcessMessages 1647", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801725, "dur": 136, "ph": "X", "name": "ReadAsync 1647", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801866, "dur": 1, "ph": "X", "name": "ProcessMessages 1804", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801868, "dur": 77, "ph": "X", "name": "ReadAsync 1804", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270801973, "dur": 42, "ph": "X", "name": "ReadAsync 1115", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802016, "dur": 1, "ph": "X", "name": "ProcessMessages 1781", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802018, "dur": 49, "ph": "X", "name": "ReadAsync 1781", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802068, "dur": 1, "ph": "X", "name": "ProcessMessages 1088", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802070, "dur": 17, "ph": "X", "name": "ReadAsync 1088", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802090, "dur": 16, "ph": "X", "name": "ReadAsync 1012", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802109, "dur": 21, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802167, "dur": 45, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802219, "dur": 1, "ph": "X", "name": "ProcessMessages 1254", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802222, "dur": 129, "ph": "X", "name": "ReadAsync 1254", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802352, "dur": 1, "ph": "X", "name": "ProcessMessages 2220", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802354, "dur": 46, "ph": "X", "name": "ReadAsync 2220", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802402, "dur": 19, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802424, "dur": 17, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802443, "dur": 107, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802551, "dur": 1, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802626, "dur": 26, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802654, "dur": 1, "ph": "X", "name": "ProcessMessages 2205", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270802655, "dur": 1260, "ph": "X", "name": "ReadAsync 2205", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270803918, "dur": 32, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270803951, "dur": 4, "ph": "X", "name": "ProcessMessages 8104", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270803956, "dur": 144, "ph": "X", "name": "ReadAsync 8104", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804101, "dur": 37, "ph": "X", "name": "ProcessMessages 2596", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804139, "dur": 35, "ph": "X", "name": "ReadAsync 2596", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804185, "dur": 1, "ph": "X", "name": "ProcessMessages 2040", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804187, "dur": 136, "ph": "X", "name": "ReadAsync 2040", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804324, "dur": 1, "ph": "X", "name": "ProcessMessages 3290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804333, "dur": 68, "ph": "X", "name": "ReadAsync 3290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804402, "dur": 1, "ph": "X", "name": "ProcessMessages 2004", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804404, "dur": 51, "ph": "X", "name": "ReadAsync 2004", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804456, "dur": 9, "ph": "X", "name": "ProcessMessages 1166", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804466, "dur": 37, "ph": "X", "name": "ReadAsync 1166", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804514, "dur": 2, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804516, "dur": 143, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804660, "dur": 2, "ph": "X", "name": "ProcessMessages 2703", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804701, "dur": 41, "ph": "X", "name": "ReadAsync 2703", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804749, "dur": 1, "ph": "X", "name": "ProcessMessages 2879", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804751, "dur": 28, "ph": "X", "name": "ReadAsync 2879", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804784, "dur": 1, "ph": "X", "name": "ProcessMessages 1267", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804785, "dur": 33, "ph": "X", "name": "ReadAsync 1267", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804822, "dur": 21, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804846, "dur": 116, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270804964, "dur": 59, "ph": "X", "name": "ReadAsync 1538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805029, "dur": 1, "ph": "X", "name": "ProcessMessages 3723", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805031, "dur": 34, "ph": "X", "name": "ReadAsync 3723", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805068, "dur": 46, "ph": "X", "name": "ReadAsync 1110", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805118, "dur": 1, "ph": "X", "name": "ProcessMessages 1312", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805119, "dur": 357, "ph": "X", "name": "ReadAsync 1312", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805479, "dur": 4, "ph": "X", "name": "ProcessMessages 7869", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805483, "dur": 107, "ph": "X", "name": "ReadAsync 7869", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805592, "dur": 109, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805703, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805704, "dur": 47, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805754, "dur": 228, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805987, "dur": 2, "ph": "X", "name": "ProcessMessages 2904", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270805989, "dur": 46, "ph": "X", "name": "ReadAsync 2904", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270806040, "dur": 1, "ph": "X", "name": "ProcessMessages 1691", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270806041, "dur": 16, "ph": "X", "name": "ReadAsync 1691", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270806060, "dur": 24, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270806086, "dur": 59, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270806148, "dur": 127, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270806277, "dur": 737, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270807017, "dur": 124, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270807143, "dur": 569, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270807720, "dur": 484, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270808206, "dur": 143, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270808351, "dur": 539, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270808893, "dur": 122, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270809016, "dur": 705, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270809731, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270809733, "dur": 740, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270810480, "dur": 631, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270811114, "dur": 978, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270812094, "dur": 551, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270812648, "dur": 126, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270812806, "dur": 1025, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270813834, "dur": 325, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270814189, "dur": 537, "ph": "X", "name": "ReadAsync 1011", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270814729, "dur": 772, "ph": "X", "name": "ReadAsync 969", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270815503, "dur": 820, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270816342, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270816344, "dur": 607, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270816954, "dur": 350, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270817344, "dur": 385, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270817749, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270817784, "dur": 10486, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270828278, "dur": 6, "ph": "X", "name": "ProcessMessages 8124", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270828298, "dur": 43, "ph": "X", "name": "ReadAsync 8124", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270828344, "dur": 3552, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270831899, "dur": 2, "ph": "X", "name": "ProcessMessages 3513", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270831902, "dur": 894, "ph": "X", "name": "ReadAsync 3513", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270832798, "dur": 1, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270832799, "dur": 494, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270833296, "dur": 1918, "ph": "X", "name": "ReadAsync 837", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270835217, "dur": 37, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270835256, "dur": 1, "ph": "X", "name": "ProcessMessages 981", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270835257, "dur": 34, "ph": "X", "name": "ReadAsync 981", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270835294, "dur": 1462, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270836759, "dur": 20, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270836781, "dur": 1708, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270838492, "dur": 683, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270839187, "dur": 24, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270839214, "dur": 634, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270839851, "dur": 17, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270839870, "dur": 677, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270840549, "dur": 697, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270841249, "dur": 419, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270841670, "dur": 27, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270841699, "dur": 195, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270841896, "dur": 39, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270841937, "dur": 127, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270842066, "dur": 89, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270842157, "dur": 188, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270842351, "dur": 5, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270842356, "dur": 185, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270842544, "dur": 697, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270843244, "dur": 179, "ph": "X", "name": "ReadAsync 936", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270843425, "dur": 154, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270843581, "dur": 838, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270844422, "dur": 330, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270844755, "dur": 599, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270845355, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270845357, "dur": 144, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270845503, "dur": 475, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270845981, "dur": 522, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270846512, "dur": 528, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270847042, "dur": 37, "ph": "X", "name": "ReadAsync 863", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270847081, "dur": 613, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270847695, "dur": 84, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270847780, "dur": 5, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270847786, "dur": 388, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270848189, "dur": 47, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270848238, "dur": 1, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270848239, "dur": 31, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270848272, "dur": 517, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270848791, "dur": 16, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270848809, "dur": 515, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270849327, "dur": 31, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270849359, "dur": 495, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270849856, "dur": 39, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270849898, "dur": 506, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270850420, "dur": 25, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270850447, "dur": 464, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270850914, "dur": 20, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270850936, "dur": 38, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270850977, "dur": 36, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851015, "dur": 50, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851067, "dur": 55, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851123, "dur": 6, "ph": "X", "name": "ProcessMessages 1311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851130, "dur": 18, "ph": "X", "name": "ReadAsync 1311", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851151, "dur": 583, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851736, "dur": 142, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270851883, "dur": 412, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852297, "dur": 37, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852335, "dur": 1, "ph": "X", "name": "ProcessMessages 1039", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852336, "dur": 32, "ph": "X", "name": "ReadAsync 1039", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852371, "dur": 14, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852393, "dur": 150, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852545, "dur": 24, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852570, "dur": 61, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852632, "dur": 1, "ph": "X", "name": "ProcessMessages 1187", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270852633, "dur": 632, "ph": "X", "name": "ReadAsync 1187", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270853267, "dur": 105, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270853375, "dur": 67, "ph": "X", "name": "ReadAsync 884", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270853446, "dur": 925, "ph": "X", "name": "ReadAsync 978", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270854372, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270854373, "dur": 40, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270854423, "dur": 32, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270854458, "dur": 246, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270854710, "dur": 167, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270854878, "dur": 255, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855134, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855168, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855169, "dur": 62, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855233, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855296, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855363, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855498, "dur": 75, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855575, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855623, "dur": 35, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855660, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855756, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855797, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855845, "dur": 20, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855867, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855928, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270855975, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856001, "dur": 280, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856283, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856284, "dur": 17, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856303, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856359, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856451, "dur": 72, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856525, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856555, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856607, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856637, "dur": 18, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856658, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856732, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856762, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856798, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856854, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856916, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856940, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270856993, "dur": 84, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857079, "dur": 43, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857124, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857173, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857247, "dur": 73, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857322, "dur": 25, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857349, "dur": 101, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857452, "dur": 70, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857524, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857526, "dur": 76, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857604, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857657, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857718, "dur": 65, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857785, "dur": 100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270857886, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858017, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858125, "dur": 75, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858201, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858248, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858297, "dur": 32, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858331, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858361, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858433, "dur": 350, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858785, "dur": 82, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858869, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270858927, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859011, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859075, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859105, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859187, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859234, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859258, "dur": 323, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859596, "dur": 24, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859621, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859660, "dur": 122, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270859785, "dur": 274, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860061, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860100, "dur": 302, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860407, "dur": 234, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860654, "dur": 10, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860666, "dur": 48, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860715, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860717, "dur": 68, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860787, "dur": 65, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860855, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270860923, "dur": 237, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861162, "dur": 85, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861250, "dur": 218, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861508, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861509, "dur": 24, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861535, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861536, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861562, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861621, "dur": 90, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861713, "dur": 10, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861724, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861781, "dur": 20, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861802, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861803, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861825, "dur": 46, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861873, "dur": 44, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270861919, "dur": 438, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862371, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862373, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862397, "dur": 32, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862431, "dur": 136, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862567, "dur": 6, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862574, "dur": 19, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862596, "dur": 86, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270862683, "dur": 344, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863043, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863044, "dur": 27, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863083, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863141, "dur": 70, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863212, "dur": 12, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863225, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863267, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863395, "dur": 39, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863437, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863489, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863571, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863573, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863632, "dur": 93, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270863728, "dur": 254, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270864015, "dur": 1555, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270865652, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270865755, "dur": 2205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270867970, "dur": 8922, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270876895, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270876896, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270877073, "dur": 1850, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270878927, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270878930, "dur": 766, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270879700, "dur": 851, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270880558, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270880563, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270880747, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270881102, "dur": 265, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270881370, "dur": 433, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270881805, "dur": 3042, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270884853, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270884856, "dur": 224, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270885082, "dur": 603, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270885688, "dur": 467, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270886171, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270886184, "dur": 1107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270887292, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270887294, "dur": 124, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270887421, "dur": 135, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270887558, "dur": 550, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270888110, "dur": 657, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270888770, "dur": 130, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270888902, "dur": 660, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270889624, "dur": 714, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270890400, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270890403, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270890487, "dur": 593, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270891086, "dur": 473, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270891562, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270891706, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270891775, "dur": 2306, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270894084, "dur": 825, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270894912, "dur": 218, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270895131, "dur": 1056, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270896247, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270896320, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270896410, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270896446, "dur": 741, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270897189, "dur": 324, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270897515, "dur": 102, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270897618, "dur": 441, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270898061, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270898134, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270898230, "dur": 238, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270898470, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270898496, "dur": 656, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270899153, "dur": 214, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270899369, "dur": 209, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270899580, "dur": 326, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270899909, "dur": 1391, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270901307, "dur": 1328, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270902637, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270902641, "dur": 2499, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270905143, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270905145, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270905216, "dur": 488, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270905712, "dur": 599, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270906314, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270906408, "dur": 1141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270907551, "dur": 1118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270908671, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270908759, "dur": 710, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270909472, "dur": 176, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270909650, "dur": 455, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270910107, "dur": 950, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270911058, "dur": 392, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270911457, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270911492, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270911562, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270911637, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270911721, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270912026, "dur": 165, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270912192, "dur": 399, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270912594, "dur": 476, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270913072, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270913291, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270913484, "dur": 136, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270913621, "dur": 159, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270913782, "dur": 959, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270914743, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270914796, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270914853, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270915000, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270915087, "dur": 69, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270915158, "dur": 107, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270915267, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270915399, "dur": 81, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270915482, "dur": 749, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270916239, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270916332, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270916401, "dur": 192, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270916596, "dur": 284, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270916883, "dur": 353, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917238, "dur": 179, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917419, "dur": 67, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917488, "dur": 93, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917583, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917786, "dur": 125, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917914, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270917952, "dur": 100, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270918055, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270918110, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270918165, "dur": 359, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270918526, "dur": 3256, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270921786, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270921788, "dur": 2877, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270924668, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270924670, "dur": 4261, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270928935, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270928938, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270929048, "dur": 604, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270929654, "dur": 271, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270929932, "dur": 16, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270929950, "dur": 2049, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270932002, "dur": 175, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270932179, "dur": 855, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270933044, "dur": 475, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270933523, "dur": 4526, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270938063, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270938066, "dur": 1581, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270939651, "dur": 202, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270939856, "dur": 400, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270940259, "dur": 906, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270941168, "dur": 2251, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270943423, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270943436, "dur": 419, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270943858, "dur": 424, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270944288, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270944292, "dur": 315, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270944610, "dur": 286, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270944898, "dur": 97, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270944998, "dur": 1199, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270946199, "dur": 661, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270946863, "dur": 156, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270947024, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270947028, "dur": 184, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270947214, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270947236, "dur": 186, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270947424, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270947498, "dur": 540, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270948040, "dur": 98, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270948140, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270948200, "dur": 656, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270948859, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270948971, "dur": 427, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270949400, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962270949402, "dur": 74808, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024259, "dur": 2, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024263, "dur": 125, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024391, "dur": 204, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024602, "dur": 2, "ph": "X", "name": "ProcessMessages 7477", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024659, "dur": 91, "ph": "X", "name": "ReadAsync 7477", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024752, "dur": 122, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024876, "dur": 107, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271024986, "dur": 94, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271025082, "dur": 1351, "ph": "X", "name": "ProcessMessages 2800", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271026434, "dur": 3125, "ph": "X", "name": "ReadAsync 2800", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271029564, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271029566, "dur": 1174, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271030744, "dur": 617, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271031364, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271031431, "dur": 613, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271032047, "dur": 1096, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271033146, "dur": 881, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271034033, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271034037, "dur": 431, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271034472, "dur": 435, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271034909, "dur": 217, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271035128, "dur": 1118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271036257, "dur": 160, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271036423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271036427, "dur": 644, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271037073, "dur": 128, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271037204, "dur": 677, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271037882, "dur": 206, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271038094, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271038097, "dur": 863, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271038962, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271038964, "dur": 569, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271039537, "dur": 304, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271039844, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271039850, "dur": 350, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271040220, "dur": 764, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271040986, "dur": 260, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271041249, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271041251, "dur": 1009, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271042262, "dur": 501, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271042769, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271042772, "dur": 1389, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271044164, "dur": 191, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271044357, "dur": 315, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271044681, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271044685, "dur": 463, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271045150, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271045196, "dur": 697, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271045895, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271045998, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271046014, "dur": 445, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271046461, "dur": 1728, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271048192, "dur": 1337, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271049533, "dur": 486, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271050020, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271050096, "dur": 30, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271050128, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271050173, "dur": 696, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271050872, "dur": 253, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051127, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051172, "dur": 94, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051270, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051297, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051342, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051425, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051504, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051580, "dur": 105, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051687, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051766, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051800, "dur": 7, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051808, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051852, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051888, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051917, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271051986, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052039, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052040, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052084, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052150, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052169, "dur": 82, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052253, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052295, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052420, "dur": 66, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052489, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052544, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052576, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052608, "dur": 22, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052632, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052718, "dur": 28, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052748, "dur": 96, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052845, "dur": 24, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052871, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052926, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271052966, "dur": 34, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053001, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053022, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053046, "dur": 6, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053053, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053073, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053125, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053189, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053207, "dur": 442, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053651, "dur": 79, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271053733, "dur": 389, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271054124, "dur": 394511, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271448640, "dur": 19, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271448660, "dur": 1063, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271449725, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271449728, "dur": 1868, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271451598, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271451600, "dur": 127, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271451728, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271451730, "dur": 182465, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634202, "dur": 1, "ph": "X", "name": "ProcessMessages 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634204, "dur": 27, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634234, "dur": 21, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634256, "dur": 29, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634287, "dur": 27, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634315, "dur": 16, "ph": "X", "name": "ProcessMessages 5339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634332, "dur": 66, "ph": "X", "name": "ReadAsync 5339", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634400, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634427, "dur": 71, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634500, "dur": 25, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634527, "dur": 93, "ph": "X", "name": "ReadAsync 8192", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634623, "dur": 8, "ph": "X", "name": "ProcessMessages 7965", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271634632, "dur": 6114, "ph": "X", "name": "ReadAsync 7965", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271640748, "dur": 2, "ph": "X", "name": "ProcessMessages 72", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271640751, "dur": 304, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271641057, "dur": 12, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271641070, "dur": 318, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271641390, "dur": 456, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 23505, "tid": 12884901888, "ts": 1748962271641848, "dur": 4650, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 23505, "tid": 243, "ts": 1748962271660187, "dur": 1481, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 23505, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 23505, "tid": 8589934592, "ts": 1748962270770026, "dur": 106790, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 23505, "tid": 8589934592, "ts": 1748962270876819, "dur": 40, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 23505, "tid": 8589934592, "ts": 1748962270876861, "dur": 3819, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 23505, "tid": 243, "ts": 1748962271661671, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 23505, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 23505, "tid": 4294967296, "ts": 1748962270707055, "dur": 940758, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748962270710957, "dur": 56012, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748962271647892, "dur": 3147, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748962271650033, "dur": 50, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 23505, "tid": 4294967296, "ts": 1748962271651116, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 23505, "tid": 243, "ts": 1748962271661682, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1748962270774749, "dur": 2747, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748962270777506, "dur": 18315, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748962270795873, "dur": 72, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1748962270795946, "dur": 85, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748962270797052, "dur": 952, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748962270803625, "dur": 828, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1748962270822547, "dur": 6228, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1748962270796036, "dur": 59068, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748962270855112, "dur": 786470, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1748962271641728, "dur": 751, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1748962270795994, "dur": 59124, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270855123, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1748962270855349, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_DB357A1C5B5CB80B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270855557, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270855630, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3BDCA2C56F5A3F82.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270855739, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270855816, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_00177D20A4B9F3ED.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270855908, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270855994, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_AAC09D6387BA9514.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270856054, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270856134, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_317A56715ABF89E0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270856242, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270856300, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_F4C05CFCBFAEF91C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270856375, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270856465, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_18DC2EF7A0439C61.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270856624, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_D296BA1279712408.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270856736, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_9191DFAD59FAEA6A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270856809, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270856884, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_548678B7412B99C9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270857004, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270857068, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_9927031187632C7E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270857229, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_D4B8FF3D259E96C1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270857351, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270857461, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_FB9A8EFABECD3061.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270857520, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270857602, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_0240B089C917E98F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270857694, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270857773, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_90AD9150DA4AFB38.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270857906, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_9790B5B5B9BAF4F9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270858002, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270858063, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748962270858570, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270858896, "dur": 1639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270860535, "dur": 450, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270861007, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_79746E5030301F94.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270861211, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748962270861277, "dur": 509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748962270861792, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1748962270861926, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748962270862118, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270862218, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270862291, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270862383, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748962270862800, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270862908, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270862963, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270863085, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748962270863400, "dur": 524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1748962270863924, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270863998, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270864115, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270865397, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270866261, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270866992, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270867688, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270868393, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270869121, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270869848, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270870668, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270871406, "dur": 1414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270872820, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270873914, "dur": 1603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270875517, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270876753, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270878474, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270879211, "dur": 19026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270898238, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270898534, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270898605, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270898951, "dur": 12794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270911746, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270912094, "dur": 1602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270913737, "dur": 3059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270916796, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270916871, "dur": 722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270917612, "dur": 26400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270944024, "dur": 1236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270945274, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270945444, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270945837, "dur": 1401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270947265, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270947372, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270947695, "dur": 786, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270948491, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270948562, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270948808, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962270949314, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1748962270949408, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962270954907, "dur": 494103, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962271449762, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1748962271450158, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748962271451807, "dur": 219, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962271634559, "dur": 470, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1748962271452453, "dur": 182581, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1748962271641113, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1748962271641183, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270795995, "dur": 59130, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270855128, "dur": 249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748962270855386, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_33EF23AB874F1C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270855494, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_F9A822B4784E7167.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270855640, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270855699, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_3DB4BB1D7CF8DED1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270855793, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270855884, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_C72CD647BFAB69C4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270855992, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_0AE5CCD911510C62.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856052, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270856122, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_B651CB6D5431C551.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856247, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_FD466DE9C9D83358.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856386, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_D51B60744B48FB82.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856477, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270856582, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F44CFBD4B87405AF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856632, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270856692, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_EE8FDF61FD2953D9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856756, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270856822, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_23B09F05B98149F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270856947, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270857026, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_4D1EEC228562B063.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270857148, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270857206, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_458C519AD2A9288F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270857310, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270857396, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_43500FCF65A89BF3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270857542, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_90196DBEEEA0E95D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270857702, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270857781, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.OSXStandalone.Extensions.dll_E38DB30ED3E699A5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270857921, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4228628CD1B97490.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270858046, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270858129, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270858574, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270859229, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1748962270859358, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270859412, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270860028, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270860085, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270860138, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270860248, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270861160, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748962270861229, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270861575, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1748962270861953, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270862195, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270862260, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270862320, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270862858, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270862910, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270862970, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270863067, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270863187, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270863356, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1748962270863869, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270863952, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270864029, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270864127, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270865036, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270865841, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270866744, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270867433, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270868128, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270868844, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270869564, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270870356, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270871129, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270872393, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270873436, "dur": 1477, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270874913, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270876282, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270877355, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270878912, "dur": 1936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270880892, "dur": 8158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748962270889050, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270889184, "dur": 629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270889813, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270889882, "dur": 979, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270890898, "dur": 5466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748962270896364, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270896697, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_EA1A6D1CB62C8BFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270896777, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270896849, "dur": 1813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270898686, "dur": 1128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270899831, "dur": 16733, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748962270916564, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270916820, "dur": 662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270917517, "dur": 23765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748962270941283, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270941606, "dur": 2114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1748962270943760, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1748962270945073, "dur": 2259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962270947332, "dur": 80490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271027823, "dur": 2174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271029997, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271030074, "dur": 2410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271032485, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271032969, "dur": 1886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271034857, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271034966, "dur": 2512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271037479, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271037575, "dur": 2602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271040178, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271040244, "dur": 2921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271043217, "dur": 2616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271045871, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271048672, "dur": 4662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1748962271053335, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271053475, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271053604, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271053670, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1748962271054602, "dur": 586941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270796001, "dur": 59129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270855135, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_5A44DDE874DD43A2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270855418, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_CD898CC368A4D4E9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270855556, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270855636, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_F3E4D7EAC2485D07.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270855906, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_0F150CB94F2F22E2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856043, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_7EFD851680B8C482.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856188, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_3A20567EF69EC9A7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856282, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270856339, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7FC5D5B6D1C0F474.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856480, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_CC9B8F296E9C6231.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856629, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_AA0CAB1786F0F828.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856756, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_282E744BB5E921C6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270856842, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270856928, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_AB798D326E6716CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270857034, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270857136, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_6ECCF42616E9E2B7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270857210, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270857295, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_6F2F737294BA2646.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270857401, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270857486, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_2AF15A5FC73B3288.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270857568, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270857644, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_C20199F83674ABBE.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270857726, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270857789, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270857912, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_C359BB03E2AB95AC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270858007, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270858091, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_8C003B92E4EC36BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270858198, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270858691, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270858757, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270858829, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270859329, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1748962270859529, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_C7D65B20A28C6399.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270859597, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270859663, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270860055, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270860117, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270860812, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270861244, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270861468, "dur": 435, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270861928, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270861985, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270862115, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270862241, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270862313, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270862771, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270862874, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270862939, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270863152, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270863514, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270863622, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270863709, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270863838, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270863895, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270863957, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270864046, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1748962270864222, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270864312, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270865167, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270866051, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270866108, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270867122, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270867818, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270868524, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270869229, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270869981, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270870847, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270871733, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270872973, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270874125, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270875686, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270876880, "dur": 1683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270878564, "dur": 3645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270882259, "dur": 5065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270887324, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270887728, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270887804, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270889296, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270889978, "dur": 5355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270895333, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270895529, "dur": 1292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270896823, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270896913, "dur": 1600, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270898534, "dur": 1050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270899605, "dur": 5382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270904987, "dur": 530, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270905571, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270905673, "dur": 3339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270909012, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270909096, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270911602, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270911860, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270912457, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270912997, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270913052, "dur": 2085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270915137, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270915240, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270915326, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll_4AD9CE269414E4CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270915402, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270915534, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_AAF6E30B590FA4FC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270915589, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270915682, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll_8EDD8A7C38E993CC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270915858, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270916641, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270917865, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270918022, "dur": 3933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270921956, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270922173, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270924040, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270925400, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270926490, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270926755, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270927862, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270928387, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270928656, "dur": 1573, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270930229, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270931421, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270932413, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270933361, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270934245, "dur": 602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270934847, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270935438, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270935887, "dur": 381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270936269, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270937081, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270938170, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270939510, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270940954, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270941279, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270941546, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270941943, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270942190, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270943249, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270944665, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270944901, "dur": 2365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962270947267, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1748962270947384, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1748962270947670, "dur": 79292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271026968, "dur": 4656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271031668, "dur": 1951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271033671, "dur": 2922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271036594, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271036651, "dur": 3712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271040364, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271040432, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonanceEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271042979, "dur": 2619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271045598, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271045651, "dur": 4781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1748962271050617, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271050711, "dur": 1172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271052090, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271052429, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271052613, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271052777, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271053205, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271053276, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271053604, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271054138, "dur": 586978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1748962271641116, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1748962271641186, "dur": 387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270796007, "dur": 59132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270855144, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_BA0B4FF91CE5F8A1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270855561, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270855624, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_542089FB00C06F3A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270855748, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_9361338275291BF9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856050, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_557EEB5E99C128BC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856149, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270856214, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_A5396449E1C706BB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856280, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270856345, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_B6303FFB36F7BFF8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856446, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270856549, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CF90820960D75094.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856644, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_C90DAD4D0E34B513.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856742, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_13682AC6E2C4EE7B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270856822, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270856909, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_3EDBDDC0989E0EE4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270857020, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270857106, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_7B6D90F67FD0A1D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270857198, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270857261, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_7FABF25E82FCDDCD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270857377, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270857468, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_1C9B2F49E4A7FD04.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270857530, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270857586, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_DB39C465F989DB9D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270857687, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270857764, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_50F42E90481F425E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270857877, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_8BB13F70623D482C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270858006, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270858070, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270858722, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270858794, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270859337, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270859389, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270859569, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270859643, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_1C1CB7F65D73ACD6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270859731, "dur": 833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270860570, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270861177, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270861350, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270861818, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1748962270862098, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862163, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862241, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862310, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270862680, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862739, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862836, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862895, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270862957, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270863109, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270863291, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270863460, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270863614, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270863708, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1748962270863927, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270864028, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270864125, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270865054, "dur": 793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270865848, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270866751, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270867441, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270868115, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270868851, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270869577, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270870367, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270871161, "dur": 1286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270872447, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270873508, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270874939, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270876323, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270877414, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270878935, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270879444, "dur": 1586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270881030, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270881716, "dur": 3158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270884886, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270885151, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_9587680413563429.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270885253, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270885349, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270885472, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270886061, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270888576, "dur": 6343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270894919, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270895375, "dur": 2193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270897604, "dur": 1089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270898710, "dur": 1255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270899983, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270901682, "dur": 6242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270907924, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270908016, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270910485, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270910571, "dur": 3239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270913810, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270913955, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Updater.ref.dll_C51C3847CE9500F9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270914018, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270914075, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_6DAF32D8644B89D6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270914136, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270914189, "dur": 3971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270918227, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270918869, "dur": 6005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270924875, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270925034, "dur": 1309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270926343, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270926640, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270928033, "dur": 455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270928489, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270929551, "dur": 3001, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1748962270932553, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270932608, "dur": 5388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnity.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270937996, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270938196, "dur": 5635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonance.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270943833, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270943930, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1748962270944285, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270944621, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270944907, "dur": 2423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962270947330, "dur": 79653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962271026990, "dur": 4825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271031817, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962271031953, "dur": 3381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271035335, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962271035390, "dur": 2527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnity.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271037964, "dur": 1845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271039851, "dur": 4122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271043973, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1748962271044025, "dur": 2782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271046818, "dur": 2910, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271049766, "dur": 4816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1748962271054599, "dur": 586961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270796014, "dur": 59145, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270855163, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A97ABBD6C696F706.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270855471, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_5637F04715021BE0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270855608, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_119E2929175A73E5.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270855746, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_1011BD4CAF44A078.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270855922, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_1B4EA2FCC997DC7B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270856044, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_3FD7D90864D1DB0B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270856260, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270856326, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_2D2E9A4895907F63.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270856440, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270856511, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_D09E55B94B6FBE69.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270856633, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_A894F34BC96CE2D6.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270856734, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_1CA607ABAD2FE49C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270856823, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270856894, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_54CEF74303048885.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270857012, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270857082, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_D95259C03EE6A7C4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270857234, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_0DC95BA72EA6C3C0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270857336, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270857426, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_57DA0DA15F72E273.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270857517, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270857577, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FA4CDBD8E90E39CA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270857684, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270857747, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1748962270858337, "dur": 2273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748962270860610, "dur": 425, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270861089, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_AA3B5DF2776E59D2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270861164, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270861308, "dur": 4323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748962270865631, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270865929, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_DE1D13444BAAFBE7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270866044, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270868343, "dur": 8578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748962270876921, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270877285, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_BD0E97F52A0DF729.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270877430, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270877512, "dur": 3897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270881409, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270881501, "dur": 7401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748962270888902, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270889247, "dur": 1457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270890738, "dur": 6789, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748962270897528, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270897973, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270898036, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1748962270898837, "dur": 3939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1748962270902776, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270902959, "dur": 6614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748962270909632, "dur": 1615, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271024459, "dur": 1044, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962270911577, "dur": 113964, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 5, "ts": 1748962271026959, "dur": 4860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271031820, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271031880, "dur": 4803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271036710, "dur": 2630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271039369, "dur": 2091, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271041497, "dur": 3297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271044794, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271044875, "dur": 2685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271047561, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271047698, "dur": 5448, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1748962271053242, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271053442, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271053619, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1748962271054194, "dur": 587367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270796020, "dur": 59157, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270855182, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_576EED2B1C54EF9B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270855487, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_86EBD7C4DEEB8CFD.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270855580, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270855647, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_FED4EADA5A496243.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270855919, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_48780D15ACA6139C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270856065, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_EE2B3985795E2BC5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270856227, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270856286, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_24BD6C6FEF5EF086.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270856365, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270856450, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_5D1BAF9C8C0FFD1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270856608, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_0BF81E52D42208B4.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270856726, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_110AF0D569B2AA65.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270856807, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270856871, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_71635C60905BE81B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270857001, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270857090, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_B5FD6E098A39A5D2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270857246, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_796E436A12A0D05D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270857327, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270857413, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_42DE6F6D94492967.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270857563, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_66BE93EAC03D0429.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270857664, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270857732, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270857885, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270857983, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270858248, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1748962270858555, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270858692, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270858777, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270859363, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748962270859571, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_336F3065DC28AE59.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270859719, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270860008, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270860090, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748962270860473, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270860783, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270861048, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748962270861233, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270861922, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270862762, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270862830, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270862888, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270862949, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270863058, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270863118, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270863175, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270863233, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270863403, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1748962270863880, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1748962270863959, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270864121, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270864194, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270865109, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270865897, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270866795, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270867485, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270868174, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270868901, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270869621, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270870425, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270871224, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270872497, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270873574, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270875029, "dur": 1348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270876377, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270877525, "dur": 1543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270879090, "dur": 5654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270884744, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270885226, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270885290, "dur": 1257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270886548, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270886608, "dur": 5163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270891771, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270891963, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_4A3A50F3774BA9F7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270892014, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270892137, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270892192, "dur": 16741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270908933, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270909215, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270911438, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270911569, "dur": 895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityResonanceEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270912491, "dur": 470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270912982, "dur": 2183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270915165, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270915410, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.Editor.ref.dll_94629FB7B2A83F68.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270915528, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270915612, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cursor.Editor.ref.dll_CD3F41468EC5D7E9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270915689, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270915882, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270916644, "dur": 1614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270918258, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270918606, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270918848, "dur": 91, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270918958, "dur": 95, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270919053, "dur": 81, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270919134, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270920045, "dur": 1503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270921549, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270923072, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270924120, "dur": 1441, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270925561, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270926302, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270926590, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270926826, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270927540, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270928205, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270928541, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270930096, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270931315, "dur": 2568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270933931, "dur": 5213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1748962270939145, "dur": 864, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270940019, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.Shared.ref.dll_0A54E06D0D53005C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270940183, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270940279, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270940917, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270940981, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270941126, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270941625, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270942267, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270943313, "dur": 68, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270943381, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270943727, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270943829, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270944120, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270944629, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270944898, "dur": 2387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270947285, "dur": 2043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962270949330, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1748962270949443, "dur": 77531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962271026979, "dur": 2868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271029847, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962271030115, "dur": 2387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271032503, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962271032586, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271033893, "dur": 2825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityResonance.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271036731, "dur": 2494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271039256, "dur": 2161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271041458, "dur": 2424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271043934, "dur": 2399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271046333, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962271046398, "dur": 3421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271049819, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962271049877, "dur": 4227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/FMODUnityEditor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1748962271054127, "dur": 586982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1748962271641110, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748962271641191, "dur": 298, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1748962271641489, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270796026, "dur": 59159, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270855188, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_B0CD21B816834329.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270855420, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_C9A2ED22AC71DAA3.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270855570, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_620BC00CDC1CB284.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270855635, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270855704, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_DBF9BD3B0D79F91E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270856048, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_6AD82FAF92A13AC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270856147, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270856202, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_73BBD5453873209A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270856432, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270856520, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_EEFB9A6B4613348E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270856638, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_A6062A75CA920C08.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270856718, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270856782, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_FF6DF2438A8881EC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270856872, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270856971, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_50D38B6F9A6D24D8.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270857069, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270857156, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_242B47D8D2C44484.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270857238, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270857317, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_3104EC13C41E6B6B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270857411, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270857494, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_DAACB4F719294F92.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270857554, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270857652, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_42EA6A51FFD2E09A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270857806, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_F395896E028284AF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270857872, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270857961, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_D9468BE09286D288.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270858055, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270858115, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270858446, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748962270858625, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748962270858709, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270858793, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270859219, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1748962270859590, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270859652, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_E81F8781B3EEDB59.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270859744, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270859983, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270860041, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270860151, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270860205, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270860402, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270860479, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1748962270860568, "dur": 939, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270861515, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270862030, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270862087, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270862156, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270862229, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270862298, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270862649, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270862845, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270862900, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270862953, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270863361, "dur": 437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1748962270863857, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270863915, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270863986, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270864111, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270865038, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270865818, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270866698, "dur": 687, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270867385, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270868062, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270868783, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270869503, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270870312, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270871113, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270872347, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270873401, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270874701, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270876231, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270877212, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270878887, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270881819, "dur": 2014, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1748962270883834, "dur": 2371, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 7, "ts": 1748962270886205, "dur": 683, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "/Applications/Unity/Hub/Editor/6000.0.39f1/Unity.app/Contents/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 7, "ts": 1748962270881141, "dur": 5747, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270886889, "dur": 4972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270891861, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270892248, "dur": 2233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270894525, "dur": 2048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270896604, "dur": 1955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270898596, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270898918, "dur": 1412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270900348, "dur": 5711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270906059, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270906154, "dur": 3606, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270909760, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270909926, "dur": 3540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270913519, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270913931, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270917543, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270917811, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270917948, "dur": 10995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270928943, "dur": 1071, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270930020, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_57B051C6BAF6DF9D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270930201, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270930278, "dur": 3160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270933486, "dur": 11876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270945362, "dur": 1167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270946544, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270946647, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270947160, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270947899, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1748962270947961, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1748962270948168, "dur": 475, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962270948648, "dur": 78342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962271026991, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271029700, "dur": 3230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271032930, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962271032999, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271034623, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962271034692, "dur": 3638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271038330, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962271038386, "dur": 3011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271041438, "dur": 3146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271044587, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962271044671, "dur": 3321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271048011, "dur": 1980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271049992, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1748962271050072, "dur": 4084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1748962271054181, "dur": 587404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270796032, "dur": 59162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270855196, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_9CEC3B310263C3AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270855566, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_BB7D6B77AADC3636.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270855638, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270855697, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_94E7D34F0A24E8EE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270855763, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270855873, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_7C523B7BB1DAFB72.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270855966, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0726B0E54E5C8C45.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270856079, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_150D0801C80CAAF8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270856180, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270856263, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_173ADBFCCFB403B8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270856381, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_1D189CF611149EDC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270856525, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270856636, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270856701, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_2A46121DF039C105.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270856770, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270856842, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_B2F5068140D067F0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270856993, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270857056, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_10C02A94B188B9C3.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270857219, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_8D74880C622CE1AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270857316, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270857387, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_4E33C3C55AB9064E.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270857553, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_5836CD6851EC9E19.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270857713, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748962270857886, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270857979, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270858101, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7ADE8004EB70550A.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270858186, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270858537, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270858923, "dur": 451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748962270859450, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1748962270859592, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270859655, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270859973, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748962270860107, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270860256, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270860506, "dur": 494, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270861024, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270861499, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270861707, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270862102, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270862201, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270862367, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1748962270862755, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270862811, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270862863, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270862915, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270862984, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270863166, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270863604, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270863664, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270863841, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1748962270863907, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270863979, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270864118, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270864176, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270865083, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270865863, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270866768, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270867460, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270868057, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270868779, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270869498, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270870310, "dur": 792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270871102, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270872309, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270873355, "dur": 1343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270874698, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270876221, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270877373, "dur": 1569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270878942, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270879384, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270879727, "dur": 5121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270884850, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270885240, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_B533E0F362656246.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270885303, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270885396, "dur": 2576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270888016, "dur": 3109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270891125, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270891358, "dur": 14552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270905910, "dur": 858, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270906856, "dur": 3046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270909903, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270910096, "dur": 1926, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270912022, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270912188, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270912593, "dur": 2495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270915089, "dur": 549, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270915642, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_8B099D8C97AF393F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270915733, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270915814, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_F37C0F1BA2EC04B9.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270915883, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270915937, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270916914, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270917222, "dur": 11525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270928748, "dur": 550, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270929314, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_C4A720C3D568CE76.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270929412, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270929481, "dur": 2892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270932426, "dur": 6734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270939161, "dur": 1460, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270940637, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270940721, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270941283, "dur": 476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270941772, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270942053, "dur": 72, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270942125, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270942883, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270943839, "dur": 101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270943980, "dur": 112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270944290, "dur": 127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270944626, "dur": 74, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270944895, "dur": 2381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962270947280, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1748962270947446, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/200b0aE.dag/FMODUnityEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1748962270947714, "dur": 79373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271027087, "dur": 1791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271028880, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271028946, "dur": 2256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271031202, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271031287, "dur": 6072, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271037408, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271040137, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271042710, "dur": 2489, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271045229, "dur": 2830, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271048089, "dur": 3241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271051330, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271051544, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271052025, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271052121, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271052709, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271053306, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271053624, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271053683, "dur": 396104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271449982, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748962271449788, "dur": 1509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271451940, "dur": 217, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271634468, "dur": 302, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1748962271452456, "dur": 182323, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1748962271641115, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library/Bee/artifacts/200b0aE.dag/post-processed/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748962271641110, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1748962271641193, "dur": 310, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1748962271643852, "dur": 1370, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 23505, "tid": 243, "ts": 1748962271662130, "dur": 2087, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 23505, "tid": 243, "ts": 1748962271664251, "dur": 1672, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 23505, "tid": 243, "ts": 1748962271657708, "dur": 11399, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}